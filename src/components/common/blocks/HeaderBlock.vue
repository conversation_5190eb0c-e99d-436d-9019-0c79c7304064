<template>
  <q-card @click="$emit('focus-fab')" class="q-mb-md header-block-container">
    <!-- Header with three-dot menu and save indicator -->
    <div class="header-top-bar">
      <div class="row items-center q-gutter-sm">
        <!-- Auto-save indicator -->
        <div v-if="isSaving" class="row items-center q-gutter-xs text-grey-6">
          <q-spinner size="12px" color="grey-6" />
          <span class="text-caption">Saving...</span>
        </div>

        <!-- JSON ID Display -->
        <div class="text-caption text-grey-5 q-ml-sm">
          Block: {{ itemBlock.id }} | Header: {{ itemBlock.headerBody?.id || 'N/A' }} | Assessment:
          {{ itemBlock.assessmentId || 'N/A' }} | Seq: {{ itemBlock.sequence || 'N/A' }} | Sec:
          {{ itemBlock.section || 'N/A' }}
        </div>

        <!-- Three-dot menu -->
        <q-btn flat round color="grey-6" size="sm" class="three-dot-menu" @click.stop="toggleMenu">
          <ThreeDots size="xs" color="grey-6" />
          <q-menu v-model="showMenu" anchor="bottom middle" self="top middle">
            <q-list style="min-width: 150px">
              <q-item clickable v-close-popup @click="onDuplicate">
                <q-item-section avatar>
                  <q-icon name="content_copy" />
                </q-item-section>
                <q-item-section>Duplicate</q-item-section>
              </q-item>
              <q-item clickable v-close-popup @click="onDelete">
                <q-item-section avatar>
                  <q-icon name="delete" />
                </q-item-section>
                <q-item-section>Delete</q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </q-btn>
      </div>
    </div>

    <!-- Title Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="titleEditor"
        :label="'หัวข้อ...'"
        :isHeader="true"
        :initialValue="titleContent"
        @update:content="updateTitle"
        @focus="isTitleFocused = true"
        @blur="handleTitleBlur"
      />
    </div>

    <!-- Description Editor -->
    <div class="input-wrapper">
      <EditorTool
        ref="descriptionEditor"
        :label="'รายละเอียด...'"
        :initialValue="descriptionContent"
        @update:content="updateDescription"
        @focus="isDescriptionFocused = true"
        @blur="handleDescriptionBlur"
      />
      <ItemBlockFooter
        v-if="props.index > 0"
        label="ข้อความ"
        @duplicate="onFooterDuplicate"
        @delete="onFooterDelete"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, watch, onUnmounted } from 'vue';
import type { Ref } from 'vue';
import EditorTool from 'src/components/common/EditorTool.vue';
import ThreeDots from 'src/components/common/ThreeDots.vue';
import type { ItemBlock } from 'src/types/models';
import ItemBlockFooter from './ItemBlockFooter.vue';
import { AssessmentService } from 'src/services/asm/assessmentService';

const props = defineProps<{
  itemBlock: ItemBlock;
  index: number;
  type?: 'quiz' | 'evaluate';
}>();

// Define emits
const emit = defineEmits([
  'blur',
  'focus-fab',
  'update:title',
  'update:description',
  'duplicate',
  'delete',
]);

// Initialize assessment service
const assessmentService = new AssessmentService(props.type || 'evaluate');

// Refs for editors
const titleEditor = ref(null);
const descriptionEditor = ref(null);

// Content refs to track values locally
const titleContent = ref<string>(props.itemBlock.headerBody?.title || '');
const descriptionContent = ref<string>(props.itemBlock.headerBody?.description || '');

// Focus state for styling
const isTitleFocused = ref(false);
const isDescriptionFocused = ref(false);

// Menu state
const showMenu = ref(false);

// Debounce timeout refs
const titleDebounceTimeout = ref<number | null>(null);
const descriptionDebounceTimeout = ref<number | null>(null);
const DEBOUNCE_DELAY = 800; // 800ms debounce delay for auto-save

// Auto-save state tracking
const lastSavedTitle = ref<string>(props.itemBlock.headerBody?.title || '');
const lastSavedDescription = ref<string>(props.itemBlock.headerBody?.description || '');
const isSaving = ref(false);

// Update functions for local content
function updateTitle(content: string) {
  titleContent.value = content;
  emit('update:title', content);

  // Trigger debounced auto-save
  debouncedAutoSave('title', content);
}

function updateDescription(content: string) {
  descriptionContent.value = content;
  emit('update:description', content);

  // Trigger debounced auto-save
  debouncedAutoSave('description', content);
}

// Clear existing timeout if any
function clearDebounceTimeout(timeoutRef: Ref<number | null>) {
  if (timeoutRef.value) {
    clearTimeout(timeoutRef.value);
    timeoutRef.value = null;
  }
}

// Auto-save functionality with debounce
function debouncedAutoSave(field: 'title' | 'description', content: string) {
  const timeoutRef = field === 'title' ? titleDebounceTimeout : descriptionDebounceTimeout;
  const lastSavedValue = field === 'title' ? lastSavedTitle.value : lastSavedDescription.value;

  // Clear existing timeout
  clearDebounceTimeout(timeoutRef);

  // Set new timeout for auto-save
  timeoutRef.value = window.setTimeout(() => {
    // Only save if content has changed and is not empty
    if (content.trim() !== '' && content !== lastSavedValue) {
      performAutoSave(field, content).catch((error) => {
        console.error(`Auto-save error for ${field}:`, error);
      });
    }
  }, DEBOUNCE_DELAY);
}

// Perform the actual auto-save operation
async function performAutoSave(field: 'title' | 'description', content: string) {
  try {
    // Validate that we have the necessary IDs
    const headerBodyId = props.itemBlock.headerBody?.id;
    const itemBlockId = props.itemBlock.id;

    if (!headerBodyId || !itemBlockId) {
      return;
    }

    isSaving.value = true;

    // Prepare the update payload for /evaluate/header-bodies/{id} endpoint
    const updatePayload = {
      itemBlockId: itemBlockId, // Required itemBlockId in body
      [field]: content,
      // Include the other field to maintain data integrity
      [field === 'title' ? 'description' : 'title']:
        field === 'title' ? descriptionContent.value : titleContent.value,
    };

    // Log the JSON payload being sent
    console.log('HeaderBlock - updateHeaderBody payload:', JSON.stringify(updatePayload, null, 2));
    console.log('HeaderBlock - headerBodyId:', headerBodyId);

    // Call the updateHeaderBody API using /evaluate/header-bodies/{id} endpoint
    const updatedHeaderBody = await assessmentService.updateHeaderBody(headerBodyId, updatePayload);

    if (updatedHeaderBody) {
      // Update the last saved values
      if (field === 'title') {
        lastSavedTitle.value = content;
      } else {
        lastSavedDescription.value = content;
      }

      // No need to refetch the entire assessment - the PATCH response contains the updated data
      // The local state is already updated and in sync with the server
    }
  } catch {
    // Auto-save failed silently
  } finally {
    isSaving.value = false;
  }
}

// Handlers for blur events that emit to parent
function handleTitleBlur() {
  isTitleFocused.value = false;

  // Emit blur event for parent component compatibility
  const content = titleContent.value;
  if (content && content.trim() !== '') {
    emit('blur', 'title', content);
  }

  // Auto-save is already handled by the updateTitle function with debounce
  // No need for additional timeout here since auto-save is triggered on input
}

function handleDescriptionBlur() {
  isDescriptionFocused.value = false;

  // Emit blur event for parent component compatibility
  const content = descriptionContent.value;
  if (content && content.trim() !== '') {
    emit('blur', 'description', content);
  }

  // Auto-save is already handled by the updateDescription function with debounce
  // No need for additional timeout here since auto-save is triggered on input
}

// Cleanup function to clear timeouts when component is unmounted
onUnmounted(() => {
  clearDebounceTimeout(titleDebounceTimeout);
  clearDebounceTimeout(descriptionDebounceTimeout);
});

// Watch for prop changes to update local state
watch(
  () => props.itemBlock.headerBody?.title,
  (newTitle) => {
    if (newTitle !== undefined && newTitle !== titleContent.value) {
      titleContent.value = newTitle;
      lastSavedTitle.value = newTitle;
    }
  },
);

watch(
  () => props.itemBlock.headerBody?.description,
  (newDescription) => {
    if (newDescription !== undefined && newDescription !== descriptionContent.value) {
      descriptionContent.value = newDescription;
      lastSavedDescription.value = newDescription;
    }
  },
);

// Menu handler functions
function toggleMenu() {
  showMenu.value = !showMenu.value;
}

function onDuplicate() {
  emit('duplicate');
}

function onDelete() {
  // Simplified validation - only check for required ID
  if (!props.itemBlock.id) {
    return;
  }

  emit('delete');
}

// Footer event handlers (same as menu handlers for consistency)
function onFooterDuplicate() {
  onDuplicate();
}

function onFooterDelete() {
  onDelete();
}
</script>

<style scoped>
.input-wrapper {
  position: relative;
  margin: 16px;
}

.header-block-container {
  position: relative;
}

.header-top-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.three-dot-menu {
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 4px;
  cursor: grab;
  outline: none;
}

.three-dot-menu:hover {
  background-color: transparent;
}

.three-dot-menu:active {
  cursor: grabbing;
}

.three-dot-menu:focus {
  outline: none;
  box-shadow: none;
}
</style>
