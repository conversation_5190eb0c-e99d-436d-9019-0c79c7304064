<template>
  <q-card v-if="category != 'HEADER'" class="q-pa-md q-ma-md evaluate-get">
    <div class="row q-ma-md">
      <q-markdown class="title">
        {{ matchedItem?.questions?.[0]?.questionText || 'ไม่พบคำถาม' }}
      </q-markdown>
      <div class="required">*</div>
    </div>
    <div class="row q-ma-md">
      <div v-if="category === 'RADIO'" class="group font-size">
        <q-radio
          :disable="isPreview"
          v-model="selectedAnswer"
          v-for="choice in matchedItem?.options"
          :key="choice.id ?? choice.optionText"
          :val="choice.id"
          :label="choice.optionText"
          color="primary"
          @update:model-value="emitAnswer"
          @blur="handleBlur"
        />
      </div>

      <div v-else-if="category === 'CHECKBOX'" class="group font-size">
        <q-checkbox
          :disable="isPreview"
          v-for="choice in matchedItem?.options"
          v-model="selectedAnswers"
          :key="choice.id ?? choice.optionText"
          :val="choice.id"
          :label="choice.optionText"
          color="primary"
          @update:model-value="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'TEXTFIELD'">
        <q-input
          :disable="isPreview"
          v-model="textAnswer"
          dense
          placeholder="คำตอบ..."
          style="min-width: 400px"
          class="font-size"
          @blur="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'GRID'" class="grid-choice">
        <q-table flat bordered :rows="rows" :columns="columns" row-key="id" hide-bottom>
          <template v-slot:body-cell="props">
            <q-td :props="props">
              <template v-if="props.col.name === 'question'">
                {{ props.row.question }}
              </template>
              <template v-else>
                <q-radio
                  :disable="isPreview"
                  :val="Number(props.col.name.replace('choice_', ''))"
                  v-model="gridAnswers[props.row.id]"
                  size="sm"
                  color="primary"
                />
              </template>
            </q-td>
          </template>
        </q-table>
      </div>
      <div v-else-if="category === 'UPLOAD'">
        <div class="q-mb-md" style="color: #9d9d9d">
          {{
            'อัปโหลด ' +
            matchedItem?.questions?.[0]?.sizeLimit +
            ' ไฟล์ ' +
            matchedItem?.questions?.[0]?.acceptFile +
            ' มากสุด ' +
            matchedItem?.questions?.[0]?.uploadLimit
          }}
        </div>
        <q-btn
          :disable="isPreview"
          icon="file_upload"
          label="เพิ่มไฟล์"
          class="file-upload font-size"
        />
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import type { Assessment } from 'src/types/models';
import type { Response } from 'src/types/models';
import type { Question } from 'src/types/models';
import response from 'src/services/evaluate/response';

import { computed, onMounted, ref, watch } from 'vue';
const props = defineProps<{
  id: number;
  category: string;
  item: Assessment;
  section: number;
  status: string;
}>();

//answer
const selectedAnswer = ref<string>('');
const selectedAnswers = ref<string[]>([]);
const textAnswer = ref('');
const gridAnswers = ref<{ [key: number]: number }>({});

const isPreview = ref(false);
const matchedItem = computed(() =>
  props.item.itemBlocks?.find((item) => item.id === props.id && item.section === props.section),
);

if (props.status === 'preview') {
  isPreview.value = true;
}
type AnswerValue = string | string[] | Record<string, number>;

const emit = defineEmits<{
  (event: 'update-answer', payload: { id: string | number; value: AnswerValue }): void;
}>();
emit('update-answer', {
  id: props.id,
  value:
    props.category === 'CHECKBOX'
      ? selectedAnswers.value
      : props.category === 'GRID'
        ? gridAnswers.value
        : props.category === 'RADIO'
          ? selectedAnswer.value
          : props.category === 'TEXTFIELD'
            ? textAnswer.value
            : '', // fallback กรณีไม่ตรงกับเงื่อนไขใดเลย
});

// Emit คำตอบเมื่อมีการเปลี่ยนแปลง

const emitAnswer = async () => {
  let value;

  if (props.category === 'GRID') {
    value = gridAnswers.value;
  } else if (props.category === 'CHECKBOX') {
    value = selectedAnswers.value; // เช่น [1, 2]
  } else if (props.category === 'TEXTFILED') {
    value = textAnswer.value;
  } else if (props.category === 'RADIO') {
    value = selectedAnswer.value;
  } else {
    value = textAnswer.value;
  }

  emit('update-answer', {
    id: props.id,
    value,
  });

  // เก็บค่าก่อนหน้าไว้
  const selectedAnswersOld = ref<string[]>([]);
  watch(
    selectedAnswers,
    (newVal, oldVal) => {
      const removed = oldVal.filter((id) => !newVal.includes(id));
      const added = newVal.filter((id) => !oldVal.includes(id));

      // 🔴 กรณี UNCHECK → DELETE เท่านั้น
      if (removed.length > 0) {
        for (const optionId of removed) {
          const numericId = parseInt(optionId, 10);
          if (isNaN(numericId)) continue;

          console.trace('🗑️ emitAnswer called (DELETE)', {
            submissionId: 1,
            questionId: props.id,
            selectedOptionId: numericId,
          });

          // await response.deleteResponse({submissionId: 1,questionId: props.id,selectedOptionId: numericId,});
        }
      }

      // 🟢 กรณี CHECK → CREATE เท่านั้น
      for (const optionId of added) {
        const numericId = parseInt(optionId, 10);
        if (isNaN(numericId)) continue;

        const payload: Response = {
          id: 0,
          submissionId: 1,
          questionId: props.id,
          selectedOptionId: numericId,
        };

        console.trace('✅ emitAnswer called (CREATE)', payload);
        //await response.createResponse(payload);
      }

      // ✅ Sync ค่าเก่าให้ใช้เปรียบเทียบในรอบถัดไป
      selectedAnswersOld.value = [...newVal];
    },
    { deep: true },
  );

  try {
    if (props.category === 'TEXTFIELD') {
      // 👉 สร้าง response แบบปกติ
      const payload: Response = {
        id: 0,
        submissionId: 1,
        questionId: props.id,
        selectedOptionId: 0,
      };

      const answerText: Question = {
        id: 0,
        itemBlockId: 0,
        questionText: textAnswer.value,
        isHeader: false,
        sequence: 0,
        score: 0,
      };
      console.trace('emitAnswer called', payload);
      console.trace('emitAnswer called', answerText);
      //api create option
    } else if (props.category === 'RADIO') {
      // 👉 สร้าง response แบบปกติ
      const payload: Response = {
        id: 0,
        submissionId: 1,
        questionId: props.id,
        selectedOptionId:
          props.category === 'RADIO' || props.category === 'GRID'
            ? parseInt(value as string, 10)
            : 0,
      };

      console.trace('emitAnswer called', payload);
      await response.createResponse(payload);
    } else if (props.category === 'GRID') {
      // 👉 สร้าง response แบบปกติ
      const payload: Response = {
        id: 0,
        submissionId: 1,
        questionId: props.id,
        selectedOptionId: props.category === 'GRID' ? parseInt(value as string, 10) : 0,
      };

      console.trace('emitAnswer called', payload);
      await response.createResponse(payload);
    }
  } catch (error) {
    console.error('Failed to send response to backend:', error);
  }
};

watch(
  gridAnswers,
  (newVal) => {
    if (props.category === 'GRID') {
      emit('update-answer', {
        id: props.id,
        value: newVal,
      });
    }
  },
  { deep: true },
);

watch(
  matchedItem,
  (newItem) => {
    if (newItem) {
      newItem.section = props.section;
    }
  },
  { immediate: true },
);

interface Row {
  id: number;
  question: string;
  [key: `choice_${number}`]: string;
}

const columns = computed<QTableColumn[]>(() => {
  const base: QTableColumn[] = [
    {
      name: 'question',
      label: 'คำถาม',
      field: 'question',
      align: 'left',
    },
  ];

  const choices: QTableColumn[] =
    matchedItem.value?.options?.map((opt, i) => ({
      name: `choice_${i}`,
      label: opt.optionText,
      field: `choice_${i}`,
      align: 'center',
    })) ?? [];

  return base.concat(choices);
});

const rows = computed<Row[]>(() => {
  if (!matchedItem.value?.questions) return [];

  return matchedItem.value.questions
    .filter((q) => !q.isHeader) // กรองเอาเฉพาะคำถามที่ไม่ใช่ header
    .map((q) => {
      const row: Row = {
        id: q.id,
        question: q.questionText,
      };

      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });
      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });

      return row;
    });
});

const STORAGE_KEY = 'draft-form';

onMounted(() => {
  console.log(props.item.itemBlocks?.[0]?.type + 'dassd');
  const saved = localStorage.getItem(STORAGE_KEY);
  if (saved) {
    const parsed = JSON.parse(saved);
    if (props.category === 'checkbox') {
      selectedAnswers.value = parsed;
    } else if (props.category === 'multipleGridChoice') {
      gridAnswers.value = parsed;
    } else {
      selectedAnswer.value = parsed;
    }
  }
});

// blur → save (เฉพาะ shortAnswer เพราะ radio/checkbox ไม่มี blur)
function handleBlur() {}

watch(
  gridAnswers,
  async () => {
    await emitAnswer();
  },
  { deep: true },
);
</script>

<style scoped lang="scss">
.title {
  font-size: 20px;
}

.required {
  color: red;
  font-size: 20px;
  margin-left: 4px;
}
.group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-upload {
  background-color: white;
  color: $primary;
  border: 1px solid;
  border-color: $surface;
}

.font-size {
  font-size: 18px;
}
.grid-choice {
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-table {
  width: 100%;
  max-width: 800px;
  table-layout: fixed;

  .label-column {
    width: 50%;
    max-width: 50%;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .option-column {
    width: calc(50% / 5);
    padding: 8px;
    text-align: center;

    &:first-child {
      border-left: none;
    }
  }
}
</style>
