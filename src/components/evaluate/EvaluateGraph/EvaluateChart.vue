<template>
  <q-card class="row justify-between items-center q-pa-md q-card-responsive">
    <div class="q-mb-md row col-12" style="align-items: center; display: flex">
      <div class="col-9" style="padding-left: 20px; padding-top: 20px">
        <strong class="title">
          {{
            props.chartData!.labels?.length === 1
              ? props.chartData!.labels[0]
              : props.chartData!.title
          }}
        </strong>
      </div>
      <q-select
        v-if="Enable"
        class="col-2"
        dense
        outlined
        v-model="selectedChart"
        :options="chartOptions"
        style="width: 150px"
        label="เลือกกราฟ"
      />
      <div class="col-1 icon-wrapper">
        <q-btn
          icon="content_copy"
          label=""
          class="white-bg"
          @click="copyChartImage"
          v-if="Enable"
        />
      </div>
    </div>

    <div class="col-12 chart-container">
      <!-- Bar Chart -->
      <BarChart
        v-if="selectedChart === 'กราฟแท่ง'"
        ref="barChartRef"
        :labels="computedChartData.labels"
        :data="computedChartData.datasets"
        class="bar-chart-responsive"
      />

      <!-- Pie Chart -->
      <div v-else-if="selectedChart === 'กราฟวงกลม'" class="pie-chart-scroll-wrapper">
        <div
          v-for="(dataset, index) in computedChartData.datasets"
          :key="index"
          class="q-mt-md pie-chart-wrapper"
        >
          <h4 v-if="props.chartData!.title !== ''" class="question-label">
            {{ dataset.label }}
          </h4>
          <div v-else class="question-label" style="height: 40px"></div>

          <PieChart
            ref="pieChartRefs"
            :labels="computedChartData.labels"
            :data="dataset.values"
            :chartLabel="dataset.label"
          />
        </div>
      </div>

      <!-- Text Chart -->
      <div v-if="selectedChart === 'ข้อความ'" class="text-chart-container">
        <div v-for="(dataset, index) in computedChartData.datasets" :key="index" class="text-item">
          <div class="text-label">{{ dataset.label }}</div>
          <div class="text-value">{{ dataset.values[0] }}</div>
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue';
import BarChart from './BarChart.vue';
import PieChart from './PieChart.vue';

const props = defineProps<{
  chartData?: {
    title: string;
    labels: string[];
    datasets: {
      label: string;
      values: number[];
    }[];
    type: 'text' | 'choice' | 'header';
  };
}>();

const Enable = ref(false);
const selectedChart = ref('ข้อความ');
const chartOptions = ['กราฟแท่ง', 'กราฟวงกลม'];

const originalChartData = props.chartData ?? {
  title: 'No Data',
  labels: [],
  datasets: [],
};

type Dataset = { label: string; values: number[] };
type ChartData = { labels: string[]; datasets: Dataset[] };

onMounted(() => {
  if (props.chartData?.type === 'text') {
    selectedChart.value = 'ข้อความ';
    Enable.value = false;
  } else if (props.chartData?.type === 'choice') {
    selectedChart.value = 'กราฟแท่ง';
    Enable.value = true;
  } else {
    selectedChart.value = 'header';
    Enable.value = false;
  }
});

function transformChartData(data: ChartData): ChartData {
  const valuesMatrix: (number | undefined)[][] = data.datasets.map((d) => d.values);
  const transposedValues = valuesMatrix[0]!.map((_, colIndex) =>
    valuesMatrix.map((row) => row[colIndex] ?? 0),
  );

  const newDatasets: Dataset[] = transposedValues.map((vals, idx) => ({
    label: data.labels[idx] ?? '',
    values: vals,
  }));

  return {
    labels: data.datasets.map((d) => d.label ?? ''),
    datasets: newDatasets,
  };
}

const computedChartData = computed<ChartData>(() => {
  return selectedChart.value === 'กราฟวงกลม'
    ? transformChartData(originalChartData)
    : originalChartData;
});

// refs สำหรับกราฟ
const barChartRef = ref<InstanceType<typeof BarChart> | null>(null);
const pieChartRefs = ref<(InstanceType<typeof PieChart> | null)[]>([]);

async function copyChartImage() {
  try {
    await nextTick();

    const chartCanvases: HTMLCanvasElement[] = [];
    const labels: string[] = [];

    if (selectedChart.value === 'กราฟแท่ง') {
      const canvas = barChartRef.value?.$el.querySelector('canvas');
      if (canvas instanceof HTMLCanvasElement) {
        chartCanvases.push(canvas);
        // ดึงชื่อกราฟจาก <strong class="title">
        const titleEl = document.querySelector('.title');
        const chartTitle = titleEl?.textContent?.trim() || (props.chartData?.title ?? '');
        labels.push(chartTitle);
      }
    } else if (selectedChart.value === 'กราฟวงกลม') {
      const wrappers = document.querySelectorAll('.pie-chart-wrapper');
      wrappers.forEach((wrapper) => {
        const label = wrapper.querySelector('h4')?.textContent ?? '';
        const canvas = wrapper.querySelector('canvas');
        if (canvas instanceof HTMLCanvasElement) {
          chartCanvases.push(canvas);
          labels.push(label);
        }
      });
    }

    if (chartCanvases.length === 0) {
      alert('ไม่พบกราฟเพื่อคัดลอก');
      return;
    }

    // รวมภาพและ label ลงใน canvas ใหม่
    const labelHeight = 24;
    const padding = 40;
    const groupSpacing = 40;

    const totalHeight =
      chartCanvases.reduce((sum, c) => sum + c.height, 0) +
      labels.length * (labelHeight + padding) +
      (labels.length - 1) * groupSpacing;

    const maxWidth = Math.max(...chartCanvases.map((c) => c.width));

    const canvas = document.createElement('canvas');
    canvas.width = maxWidth + 20;
    canvas.height = totalHeight;

    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('ไม่สามารถสร้าง context');

    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    ctx.font = '20px Arial';
    ctx.fillStyle = 'black';
    ctx.textBaseline = 'top';

    let currentY = 0;
    chartCanvases.forEach((chartCanvas, index) => {
      const label = labels[index] || '';
      if (label) {
        ctx.fillText(label, 10, currentY);
        currentY += labelHeight + padding;
      }
      ctx.drawImage(chartCanvas, 0, currentY);
      currentY += chartCanvas.height + groupSpacing;
    });

    const imageDataUrl = canvas.toDataURL('image/png');
    const response = await fetch(imageDataUrl);
    const blob = await response.blob();

    const clipboardItem = new ClipboardItem({ 'image/png': blob });
    await navigator.clipboard.write([clipboardItem]);

    alert('กราฟถูกคัดลอกแล้ว');
  } catch (error) {
    console.error('เกิดข้อผิดพลาดในการคัดลอกภาพ:', error);
    alert('ไม่สามารถคัดลอกกราฟได้');
  }
}
</script>

<style scoped lang="scss">
.q-card-responsive {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid;
}

.chart-container {
  padding: 0 5%;
  box-sizing: border-box;
  max-height: 600px;
  overflow-y: auto;
}

.pie-chart-scroll-wrapper {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
}

.pie-chart-wrapper {
  width: 100%;
  max-width: 600px; /* ลดขนาดความกว้าง */
  margin: 10px auto; /* เพิ่มระยะห่างระหว่างกราฟ */
  padding: 10px; /* เพิ่ม padding */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.pie-chart-wrapper canvas {
  width: 80% !important; /* ปรับขนาดให้เล็กลง */
  height: auto !important;
  max-height: 300px; /* จำกัดความสูง */
  object-fit: contain;
}

.bar-chart-responsive {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.white-bg {
  width: 20px !important;
  background-color: white !important;
  color: black !important;
}

.white-bg .q-icon {
  color: black !important;
}

.icon-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 4px;
  height: 100%;
}

.question-label {
  display: block;
  width: 100%;
  font-size: 20px;
  text-align: left !important;
  margin: 0 0 8px 0;
  padding-left: 0;
}

.text-chart-container {
  max-height: 300px;
  overflow-y: auto;
}

.text-item {
  background-color: lightgray;
  margin: 5px 0;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
}

.text-label,
.text-value {
  padding: 5px 20px;
}

.text-label {
  flex: 1;
}

.text-value {
  text-align: right;
  padding-right: 50px;
}

.title {
  font-size: 20px;
  font-weight: bold;
}
</style>
