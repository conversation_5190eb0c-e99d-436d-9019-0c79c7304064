<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserTextBlock
          v-if="evaluateItem.headerBody"
          :item="evaluateItem"
          :title="title || 'ไม่มีข้อมูล'"
          :description="description || 'ไม่มีข้อมูล'"
        />
      </div>

      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserQuestionBlock
          :id="evaluateItem.id"
          :item="editFormData"
          :category="evaluateItem.type"
          :section="evaluateItem.section"
          status="preview"
          @update-answer="handleAnswer"
        />
        <UserImageBlock
          v-if="evaluateItem.type === 'IMAGE'"
          :title="evaluateItem.headerBody?.title || ''"
          :image-url="evaluateItem.imageBody?.imagePath || ''"
          @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
          @update:image-url="evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)"
        />
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <q-btn v-if="isPreview" label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <q-btn v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <q-btn v-show="hideButton" label="หน้าต่อไป" @click="nextSection" />
      <div class="col-auto">
        <q-btn v-if="!hideButton && isPreview" label="ส่งแบบสอบถาม" @click="submitForm" />
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import { computed, onMounted, ref, watch, watchEffect } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { Assessment } from 'src/types/models';
//import router from 'src/router';
import { useRoute } from 'vue-router';

const route = useRoute();
const editFormData = ref<Assessment>();
const checkPage = ref<Assessment>();
const isPreview = ref(true);
const evaluateId = ref(0);
const currentSection = ref(1);

watch(
  () => route.params,
  (params) => {
    evaluateId.value = Number(params.id);
    if (String(params.status) === 'preview') {
      isPreview.value = false;
    }
  },
  { immediate: true },
);

onMounted(async () => {
  const res = await new AssessmentService('evaluate').getFormByIdWithSec(
    Number(route.params.id),
    1,
  );
  editFormData.value = res.data;
  console.log(editFormData.value);
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number>;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
  console.log(answers.value);
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
});

const hideButton = ref(true);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  const res = await new AssessmentService('evaluate').getFormByIdWithSec(
    Number(route.params.id),
    currentSection.value,
  );
  editFormData.value = res.data;

  const check = await new AssessmentService('evaluate').getFormByIdWithSec(
    Number(route.params.id),
    currentSection.value + 1,
  );
  checkPage.value = check.data;
  console.log(checkPage.value, 'eiei');
  if (!checkPage.value) {
    hideButton.value = false;
  }
};

const nextSection = async () => {
  console.log(currentSection.value);
  await updateSection(currentSection.value + 1);
};

const previousSection = async () => {
  if (currentSection.value > 1) {
    currentSection.value--;
    await updateSection(currentSection.value);
  }
};
const STORAGE_KEY = 'draft-form';
const clearForm = () => {
  localStorage.removeItem(STORAGE_KEY); // ลบค่าที่เก็บไว้
};

const submitForm = () => {
  console.log('Submitting form');
};
const title = computed(
  () =>
    editFormData.value?.itemBlocks?.find((item) => item.section === 1)?.headerBody?.title?.trim() ||
    'ไม่มีข้อมูล',
);

const description = computed(
  () =>
    editFormData.value?.itemBlocks
      ?.find((item) => item.section === 1)
      ?.headerBody?.description?.trim() || 'ไม่มีข้อมูล',
);
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>
