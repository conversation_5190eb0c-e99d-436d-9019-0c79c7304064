<template>
  <q-page class="bg-page-primary">
    <AsmMenuTab :menu="defaultAsmTabsMenu" v-model="selectedTab" />
    <q-tab-panels v-model="selectedTab" animated>
      <q-tab-panel
        v-for="tab in defaultAsmTabsMenu"
        :name="tab.name ?? ''"
        :key="tab.name ?? ''"
        class="bg-page-primary"
      >
        <component :is="componentMap[tab.name as string]" v-if="selectedTab === tab.name" />
      </q-tab-panel>
    </q-tab-panels>
  </q-page>
</template>

<script setup lang="ts">
import AsmMenuTab from 'src/components/common/AsmMenuTab.vue';
import { defaultAsmTabsMenu } from 'src/data/menu';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { onMounted, ref, defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const id = route.params.id;
const hash = route.hash;
const evaluateFormStore = useEvaluateFormStore();
const asmStore = useAssessmentStore();
const selectedTab = ref('questions');

onMounted(() => {
  if (hash) {
    selectedTab.value = hash.replace('#', '');
  } else {
    selectedTab.value = 'questions';
  }
});

const componentMap: Record<string, ReturnType<typeof defineAsyncComponent>> = {
  questions: defineAsyncComponent(() => import('./tabs/EvaluateEditorView.vue')),
  replies: defineAsyncComponent(() => import('./tabs/EvaluateReplyView.vue')),
  settings: defineAsyncComponent(() => import('./tabs/EvaluateSettingView.vue')),
};

onMounted(async () => {
  try {
    // Check if assessment is already in store (from creation flow)
    if (evaluateFormStore.currentAssessment?.id === Number(id)) {
      console.log('Assessment already in store from creation:', {
        assessmentId: evaluateFormStore.currentAssessment.id,
        itemBlocks: evaluateFormStore.currentAssessment.itemBlocks?.map((block) => ({
          id: block.id,
          type: block.type,
          assessmentId: block.assessmentId,
        })),
      });
      return;
    }

    // Otherwise, fetch from backend
    await evaluateFormStore.fetchAssessmentById(Number(id));

    const res = await new AssessmentService('evaluate').fetchOne(Number(id));

    // Debug: Log the fetched assessment data
    console.log('🔍 EvaluateEditorPage fetched assessment:', {
      assessmentId: res.id,
      itemBlocksCount: res.itemBlocks?.length || 0,
      itemBlocks:
        res.itemBlocks?.map((block) => ({
          id: block.id,
          type: block.type,
          hasQuestions: !!block.questions,
          questionsCount: block.questions?.length || 0,
          questions:
            block.questions?.map((q) => ({
              id: q.id,
              questionText: q.questionText,
            })) || [],
        })) || [],
    });

    asmStore.setCurrentAssessment(res);
  } catch {
    // Failed to load assessment
  }
});
</script>

<style scoped></style>
