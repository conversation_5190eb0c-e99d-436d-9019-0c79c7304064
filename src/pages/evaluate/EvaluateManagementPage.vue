<template>
  <q-page padding>
    <div class="row items-center justify-between q-mb-md">
      <div class="body">การจัดการแบบสอบถาม</div>
      <div class="row items-center q-gutter-sm">
        <SearchBar />
        <q-btn
          label="สร้าง"
          icon="add"
          class="text-white"
          color="accent"
          s
          @click="onClickCreate"
        />
      </div>
    </div>
    <EvaluateTable />
  </q-page>
</template>

<script setup lang="ts">
import EvaluateTable from 'src/components/evaluate/EvaluateTable.vue';
import SearchBar from 'src/components/SearchBar.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAuthStore } from 'src/stores/auth';
import { useEvaluateFormStore } from 'src/stores/evaluate/form';
import { api } from 'src/boot/axios';
import type { ItemBlock } from 'src/types/models';

async function onClickCreate() {
  try {
    const user = useAuthStore().getCurrentUser();
    const evaluateFormStore = useEvaluateFormStore();

    // Step 1: Create Assessment
    const assessmentResponse = await new AssessmentService('evaluate').createOne({
      creatorUserId: user?.id || 1,
      programId: 1,
      type: 'EVALUATE',
    });

    // Step 2: Try to Create Header Block (Expected to fail due to backend bug)
    let headerCreated = false;
    try {
      await api.post<ItemBlock>('/item-blocks/block', {
        assessmentId: assessmentResponse.id,
        type: 'HEADER',
        sequence: 1,
      });
      headerCreated = true;
    } catch {
      // Header creation failed - expected due to backend bug
    }

    // Step 3: Create Radio ItemBlock (Should work)
    await api.post<ItemBlock>('/item-blocks/block', {
      assessmentId: assessmentResponse.id,
      type: 'RADIO',
      sequence: headerCreated ? 2 : 1,
    });

    // Store the created assessment immediately in the evaluate form store
    evaluateFormStore.currentAssessment = assessmentResponse;

    // CRITICAL: Fetch the complete assessment data including itemBlocks before navigation
    try {
      await evaluateFormStore.fetchAssessmentById(assessmentResponse.id);

      // Validate that we have itemBlocks before proceeding
      if (
        !evaluateFormStore.currentAssessment?.itemBlocks ||
        evaluateFormStore.currentAssessment.itemBlocks.length === 0
      ) {
        // No itemBlocks found - BlockCreator will handle this
      }
    } catch {
      // Failed to fetch complete assessment data
    }

    await router.push({
      name: 'evaluate-id',
      query: { mode: 'edit' },
      params: { id: assessmentResponse.id },
      hash: '#questions',
    });
  } catch (error) {
    console.error('Failed to create evaluation:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
  }
}
</script>

<style scoped></style>
