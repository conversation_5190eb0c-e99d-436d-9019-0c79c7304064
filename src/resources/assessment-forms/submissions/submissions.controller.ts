import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SubmissionsService } from './submissions.service';
import type { UpdateSubmissionDto } from '../dto/updates/update-submission.dto';
import type { CreateSubmissionDto } from '../dto/creates/create-submission.dto';
import { StartQuizDto } from '../dto/start-quiz.dto';

@ApiTags('ASM - Submissions')
@Controller('submissions')
export class SubmissionsController {
  constructor(private readonly submissionsService: SubmissionsService) {}
  @Post()
  @ApiOperation({ summary: 'สร้าง submission ใหม่' })
  @ApiResponse({ 
    status: 201, 
    description: 'สร้าง submission สำเร็จ',
    schema: {
      example: {
        // Request example
        request: {
          userId: 1,
          assessmentId: 4,
          startAt: "2025-05-28T10:00:00.000Z"
        },
        // Response example
        response: {
          id: 13,
          userId: 1,
          assessmentId: 4,
          startAt: "2025-05-28T10:00:00.000Z",
          endAt: null,
          assessment: {
            id: 4,
            name: "แบบทดสอบครั้งที่ 1",
            type: "QUIZ",
            status: true,
            submitLimit: 1,
            totalScore: 100,
            passRatio: 0.6
          },
          user: {
            id: 1,
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>"
          },
          responses: []
        }
      }
    }
  })
  @ApiOperation({
    description: `
    สร้าง submission ใหม่สำหรับการทำแบบทดสอบ
    - ตรวจสอบว่า assessment ที่ระบุมีอยู่จริง
    - ตรวจสอบ submitLimit ว่าผู้ใช้สามารถทำแบบทดสอบได้
    - บันทึกเวลาเริ่มทำแบบทดสอบ
    `
  })
  async create(@Body() createSubmissionDto: CreateSubmissionDto) {
    try {
      return await this.submissionsService.create(createSubmissionDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูล submissions ทั้งหมด' })
  @ApiResponse({ 
    status: 200, 
    description: 'ดึงข้อมูลสำเร็จ'
  })
  async findAll() {
    return this.submissionsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูล submission ตาม ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'ดึงข้อมูลสำเร็จ'
  })
  @ApiResponse({ status: 404, description: 'ไม่พบข้อมูล submission' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    const submission = await this.submissionsService.findOne(id);
    if (!submission) {
      throw new HttpException('Submission not found', HttpStatus.NOT_FOUND);
    }
    return submission;
  }

  @Patch(':id')
  @ApiOperation({ summary: 'อัปเดตข้อมูล submission' })
  @ApiResponse({ 
    status: 200, 
    description: 'อัปเดตข้อมูลสำเร็จ'
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSubmissionDto: UpdateSubmissionDto,
  ) {
    try {
      return await this.submissionsService.update(id, updateSubmissionDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบ submission' })
  @ApiResponse({ status: 200, description: 'ลบข้อมูลสำเร็จ' })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<void> {
    try {
      await this.submissionsService.remove(id);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Post('start-assessment')
  @ApiOperation({ summary: 'เริ่มทำแบบทดสอบ' })
  @ApiResponse({ 
    status: 201, 
    description: 'เริ่มทำแบบทดสอบสำเร็จ'
  })
  async startAssessment(@Body() startQuizDto: StartQuizDto) {
    try {
      return await this.submissionsService.startAssessment(startQuizDto);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Patch('submit-assessment/:submissionId')
  @ApiOperation({ summary: 'ส่งแบบทดสอบ' })
  @ApiResponse({ 
    status: 200, 
    description: 'ส่งแบบทดสอบสำเร็จ'
  })
  async submitAssessment(
    @Param('submissionId', ParseIntPipe) submissionId: number
  ) {
    try {
      return await this.submissionsService.submitAssessment(submissionId);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Get('quiz/score/:submissionId')
  @ApiOperation({ summary: 'ดูคะแนนแบบทดสอบ' })
  @ApiResponse({ 
    status: 200, 
    description: 'ดึงข้อมูลคะแนนสำเร็จ',
    schema: {
      type: 'object',
      properties: {
        score: {
          type: 'number',
          description: 'คะแนนที่ได้'
        },
        totalScore: {
          type: 'number',
          description: 'คะแนนเต็ม'
        },
        isPassed: {
          type: 'boolean',
          description: 'สถานะการสอบผ่าน'
        }
      }
    }
  })
  async getQuizScore(
    @Param('submissionId', ParseIntPipe) submissionId: number
  ) {
    try {
      return await this.submissionsService.getQuizScore(submissionId);
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }
}
